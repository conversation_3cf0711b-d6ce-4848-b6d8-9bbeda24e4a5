/**
 * Simple test script to verify the checkout API changes
 * This tests that the API now accepts POST requests with JSON body
 * instead of GET requests with query parameters
 */

const http = require('http');

// Test data for token purchase
const tokenPurchaseData = {
  user_id: 'test_user_123',
  email: '<EMAIL>',
  tokens: 100,
  price: 9.99,
  product_name: '100 Tokens'
};

// Test data for subscription purchase
const subscriptionData = {
  user_id: 'test_user_123',
  email: '<EMAIL>',
  selected_plan: 'monthly'
};

function makePostRequest(data, testName) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(data);
    
    const options = {
      hostname: 'localhost',
      port: 8080,
      path: '/api/create_checkout_session',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          console.log(`\n${testName} Test Results:`);
          console.log(`Status Code: ${res.statusCode}`);
          console.log(`Response:`, parsedData);
          
          if (res.statusCode === 200 && parsedData.success) {
            console.log(`✅ ${testName} test PASSED`);
          } else {
            console.log(`❌ ${testName} test FAILED`);
          }
          
          resolve({ statusCode: res.statusCode, data: parsedData });
        } catch (error) {
          console.log(`❌ ${testName} test FAILED - Invalid JSON response`);
          console.log('Raw response:', responseData);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      console.log(`❌ ${testName} test FAILED - Connection error:`, error.message);
      reject(error);
    });

    req.write(postData);
    req.end();
  });
}

async function runTests() {
  console.log('🧪 Testing Checkout API Changes');
  console.log('================================');
  
  try {
    // Test token purchase
    await makePostRequest(tokenPurchaseData, 'Token Purchase');
    
    // Wait a bit between requests
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Test subscription purchase
    await makePostRequest(subscriptionData, 'Subscription Purchase');
    
    console.log('\n🎉 All tests completed!');
    console.log('\nNote: These tests verify that the API accepts POST requests with JSON body.');
    console.log('Actual Stripe integration would require valid Stripe configuration.');
    
  } catch (error) {
    console.error('Test execution failed:', error);
  }
}

// Check if server is running before running tests
function checkServerHealth() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 8080,
      path: '/api/health',
      method: 'GET',
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      if (res.statusCode === 200 || res.statusCode === 404) {
        // 404 is fine, it means server is running but no health endpoint
        resolve(true);
      } else {
        reject(new Error(`Server responded with status ${res.statusCode}`));
      }
    });

    req.on('error', (error) => {
      reject(new Error(`Cannot connect to server: ${error.message}`));
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Server connection timeout'));
    });

    req.end();
  });
}

// Main execution
async function main() {
  try {
    console.log('🔍 Checking if backend server is running on port 8080...');
    await checkServerHealth();
    console.log('✅ Server is running');
    
    await runTests();
  } catch (error) {
    console.log('❌ Server check failed:', error.message);
    console.log('\n💡 Please start the backend server first:');
    console.log('   cd backend && npm start');
  }
}

main();
